package com.datatech.slgzt.utils;

import com.datatech.slgzt.utils.PhysicalMachineImportValidator.PhysicalMachineData;
import com.datatech.slgzt.utils.PhysicalMachineImportValidator.ValidationError;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 物理机导入数据验证工具类测试
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@DisplayName("物理机导入数据验证工具类测试")
class PhysicalMachineImportValidatorTest {

    private List<String> existingDeviceIds;
    private List<String> businessSystemNames;
    private List<String> resourcePoolNames;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        existingDeviceIds = new ArrayList<>(Arrays.asList("existing-device-001", "existing-device-002"));
        businessSystemNames = new ArrayList<>(Arrays.asList("网络运维AI+Paas平台", "云资源管理平台", "数据中心管理系统"));
        resourcePoolNames = new ArrayList<>(Arrays.asList("平台云-萧山02", "创新云-杭州01", "测试云-北京01"));
    }

    @Test
    @DisplayName("测试有效数据验证 - 无错误")
    void testValidDataValidation() {
        PhysicalMachineData validData = createValidPhysicalMachineData();

        List<ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                validData, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.isEmpty(), "有效数据不应该有验证错误");
    }

    @Test
    @DisplayName("测试必填字段验证")
    void testRequiredFieldsValidation() {
        PhysicalMachineData invalidData = new TestPhysicalMachineData();
        // 所有字段都为空

        List<ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidData, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertFalse(errors.isEmpty(), "空数据应该有验证错误");
        
        // 验证必填字段错误
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("物理机名称")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("显卡类型")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("显卡型号")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("数量")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("资源池")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("开通时间")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("到期时间")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("申请人")));
        assertTrue(errors.stream().anyMatch(e -> e.getFieldName().equals("配置项编号")));
    }

    @Test
    @DisplayName("测试显卡类型验证")
    void testGpuCardTypeValidation() {
        PhysicalMachineData invalidGpuType = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) invalidGpuType).setGpuCardType("INVALID_GPU");

        List<ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidGpuType, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("显卡类型") && e.getErrorMessage().contains("只能填写NPU或GPU")));
    }

    @Test
    @DisplayName("测试数量验证")
    void testGpuNumValidation() {
        // 测试非数字
        PhysicalMachineData invalidNum1 = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) invalidNum1).setGpuNum("abc");

        List<ValidationError> errors1 = PhysicalMachineImportValidator.validateRow(
                invalidNum1, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors1.stream().anyMatch(e -> 
                e.getFieldName().equals("数量") && e.getErrorMessage().contains("必须为正整数")));

        // 测试负数
        PhysicalMachineData invalidNum2 = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) invalidNum2).setGpuNum("-5");

        List<ValidationError> errors2 = PhysicalMachineImportValidator.validateRow(
                invalidNum2, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors2.stream().anyMatch(e -> 
                e.getFieldName().equals("数量") && e.getErrorMessage().contains("必须为正整数")));

        // 测试零
        PhysicalMachineData invalidNum3 = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) invalidNum3).setGpuNum("0");

        List<ValidationError> errors3 = PhysicalMachineImportValidator.validateRow(
                invalidNum3, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors3.stream().anyMatch(e -> 
                e.getFieldName().equals("数量") && e.getErrorMessage().contains("必须为正整数")));
    }

    @Test
    @DisplayName("测试IP地址格式验证")
    void testIpAddressValidation() {
        // 测试无效IPv4
        PhysicalMachineData invalidIp1 = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) invalidIp1).setIp("999.999.999.999");

        List<ValidationError> errors1 = PhysicalMachineImportValidator.validateRow(
                invalidIp1, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors1.stream().anyMatch(e -> 
                e.getFieldName().equals("IP地址") && e.getErrorMessage().contains("格式不正确")));

        // 测试有效IPv4
        PhysicalMachineData validIp = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) validIp).setIp("*************");

        List<ValidationError> errors2 = PhysicalMachineImportValidator.validateRow(
                validIp, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertFalse(errors2.stream().anyMatch(e -> e.getFieldName().equals("IP地址")));
    }

    @Test
    @DisplayName("测试日期格式验证")
    void testDateFormatValidation() {
        // 测试无效日期格式
        PhysicalMachineData invalidDate = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) invalidDate).setResourceApplyTime("2024-06-21");

        List<ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidDate, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("开通时间") && e.getErrorMessage().contains("格式不正确")));
    }

    @Test
    @DisplayName("测试设备ID唯一性验证")
    void testDeviceIdUniquenessValidation() {
        PhysicalMachineData duplicateDevice = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) duplicateDevice).setDeviceId("existing-device-001");

        List<ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                duplicateDevice, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("配置项编号") && e.getErrorMessage().contains("已存在")));
    }

    @Test
    @DisplayName("测试业务系统存在性验证")
    void testBusinessSystemExistsValidation() {
        PhysicalMachineData invalidBusiness = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) invalidBusiness).setBusinessSysName("不存在的业务系统");

        List<ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidBusiness, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("业务系统") && e.getErrorMessage().contains("不存在")));
    }

    @Test
    @DisplayName("测试资源池存在性验证")
    void testResourcePoolExistsValidation() {
        PhysicalMachineData invalidPool = createValidPhysicalMachineData();
        ((TestPhysicalMachineData) invalidPool).setResourcePoolName("不存在的资源池");

        List<ValidationError> errors = PhysicalMachineImportValidator.validateRow(
                invalidPool, 2, existingDeviceIds, businessSystemNames, resourcePoolNames);

        assertTrue(errors.stream().anyMatch(e -> 
                e.getFieldName().equals("资源池") && e.getErrorMessage().contains("不存在")));
    }

    @Test
    @DisplayName("测试日期解析工具方法")
    void testParseDateMethod() {
        // 测试有效日期
        LocalDateTime validDate = PhysicalMachineImportValidator.parseDate("2024年6月21日");
        assertNotNull(validDate);
        assertEquals(2024, validDate.getYear());
        assertEquals(6, validDate.getMonthValue());
        assertEquals(21, validDate.getDayOfMonth());

        // 测试无效日期
        LocalDateTime invalidDate = PhysicalMachineImportValidator.parseDate("invalid-date");
        assertNull(invalidDate);

        // 测试空字符串
        LocalDateTime emptyDate = PhysicalMachineImportValidator.parseDate("");
        assertNull(emptyDate);

        // 测试null
        LocalDateTime nullDate = PhysicalMachineImportValidator.parseDate(null);
        assertNull(nullDate);
    }

    @Test
    @DisplayName("测试CPU和内存组合工具方法")
    void testCombineSpecMethod() {
        // 测试正常组合
        String spec1 = PhysicalMachineImportValidator.combineSpec("192", "2048");
        assertEquals("192|2048", spec1);

        // 测试部分为空
        String spec2 = PhysicalMachineImportValidator.combineSpec("192", "");
        assertEquals("192|", spec2);

        String spec3 = PhysicalMachineImportValidator.combineSpec("", "2048");
        assertEquals("|2048", spec3);

        // 测试全部为空
        String spec4 = PhysicalMachineImportValidator.combineSpec("", "");
        assertNull(spec4);

        // 测试null
        String spec5 = PhysicalMachineImportValidator.combineSpec(null, null);
        assertNull(spec5);
    }

    /**
     * 创建有效的物理机数据用于测试
     */
    private PhysicalMachineData createValidPhysicalMachineData() {
        TestPhysicalMachineData data = new TestPhysicalMachineData();
        data.setDeviceName("TEST-PM-001");
        data.setHandoverStatus("已交维");
        data.setOsVersion("CentOS 7.6");
        data.setGpuCardType("NPU");
        data.setGpuType("910B");
        data.setGpuNum("8");
        data.setCpu("192");
        data.setMemory("2048");
        data.setDataDisk("96960GB");
        data.setIp("*************");
        data.setApplyTime("两年");
        data.setBusinessSysName("网络运维AI+Paas平台");
        data.setCloudPlatform("平台云");
        data.setResourcePoolName("平台云-萧山02");
        data.setResourceApplyTime("2024年6月21日");
        data.setExpireTime("2026年6月21日");
        data.setApplyUserName("测试用户");
        data.setDeviceId("test-device-001");
        return data;
    }

    /**
     * 测试用的PhysicalMachineData实现类
     */
    private static class TestPhysicalMachineData implements PhysicalMachineData {
        private String deviceName;
        private String handoverStatus;
        private String osVersion;
        private String gpuCardType;
        private String gpuType;
        private String gpuNum;
        private String cpu;
        private String memory;
        private String dataDisk;
        private String ip;
        private String applyTime;
        private String businessSysName;
        private String cloudPlatform;
        private String resourcePoolName;
        private String resourceApplyTime;
        private String expireTime;
        private String applyUserName;
        private String deviceId;

        // Getters and Setters
        @Override public String getDeviceName() { return deviceName; }
        public void setDeviceName(String deviceName) { this.deviceName = deviceName; }

        @Override public String getHandoverStatus() { return handoverStatus; }
        public void setHandoverStatus(String handoverStatus) { this.handoverStatus = handoverStatus; }

        @Override public String getOsVersion() { return osVersion; }
        public void setOsVersion(String osVersion) { this.osVersion = osVersion; }

        @Override public String getGpuCardType() { return gpuCardType; }
        public void setGpuCardType(String gpuCardType) { this.gpuCardType = gpuCardType; }

        @Override public String getGpuType() { return gpuType; }
        public void setGpuType(String gpuType) { this.gpuType = gpuType; }

        @Override public String getGpuNum() { return gpuNum; }
        public void setGpuNum(String gpuNum) { this.gpuNum = gpuNum; }

        @Override public String getCpu() { return cpu; }
        public void setCpu(String cpu) { this.cpu = cpu; }

        @Override public String getMemory() { return memory; }
        public void setMemory(String memory) { this.memory = memory; }

        @Override public String getDataDisk() { return dataDisk; }
        public void setDataDisk(String dataDisk) { this.dataDisk = dataDisk; }

        @Override public String getIp() { return ip; }
        public void setIp(String ip) { this.ip = ip; }

        @Override public String getApplyTime() { return applyTime; }
        public void setApplyTime(String applyTime) { this.applyTime = applyTime; }

        @Override public String getBusinessSysName() { return businessSysName; }
        public void setBusinessSysName(String businessSysName) { this.businessSysName = businessSysName; }

        @Override public String getCloudPlatform() { return cloudPlatform; }
        public void setCloudPlatform(String cloudPlatform) { this.cloudPlatform = cloudPlatform; }

        @Override public String getResourcePoolName() { return resourcePoolName; }
        public void setResourcePoolName(String resourcePoolName) { this.resourcePoolName = resourcePoolName; }

        @Override public String getResourceApplyTime() { return resourceApplyTime; }
        public void setResourceApplyTime(String resourceApplyTime) { this.resourceApplyTime = resourceApplyTime; }

        @Override public String getExpireTime() { return expireTime; }
        public void setExpireTime(String expireTime) { this.expireTime = expireTime; }

        @Override public String getApplyUserName() { return applyUserName; }
        public void setApplyUserName(String applyUserName) { this.applyUserName = applyUserName; }

        @Override public String getDeviceId() { return deviceId; }
        public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
    }
}
