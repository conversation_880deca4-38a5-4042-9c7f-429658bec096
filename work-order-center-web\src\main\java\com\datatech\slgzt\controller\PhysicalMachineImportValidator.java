package com.datatech.slgzt.controller;

import com.datatech.slgzt.enums.ApprovalTimeEnum;
import com.datatech.slgzt.enums.HAndoverStatusEnum;
import com.datatech.slgzt.model.vo.resource.PhysicalMachineImportVO;
import com.datatech.slgzt.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 物理机导入数据验证工具类
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
public class PhysicalMachineImportValidator {

    /**
     * 日期格式：yyyy年M月d日
     */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy年M月d日");

    /**
     * 验证单行数据
     *
     * @param vo 导入数据VO
     * @param rowNumber 行号
     * @param existingDeviceIds 已存在的设备ID列表
     * @param businessSystemNames 有效的业务系统名称列表
     * @param resourcePoolNames 有效的资源池名称列表
     * @return 验证错误列表
     */
    public static List<ValidationError> validateRow(PhysicalMachineImportVO vo, int rowNumber,
                                                    List<String> existingDeviceIds,
                                                    List<String> businessSystemNames,
                                                    List<String> resourcePoolNames) {
        List<ValidationError> errors = new ArrayList<>();

        // 验证必填字段
        validateRequiredFields(vo, rowNumber, errors);

        // 验证枚举值
        validateEnumValues(vo, rowNumber, errors);

        // 验证显卡类型
        validateGpuCardType(vo, rowNumber, errors);

        // 验证数量
        validateGpuNum(vo, rowNumber, errors);

        // 验证硬盘字段
        validateDataDisk(vo, rowNumber, errors);

        // 验证IP地址格式
        validateIpAddress(vo, rowNumber, errors);

        // 验证日期格式
        validateDateFormats(vo, rowNumber, errors);

        // 验证设备ID唯一性
        validateDeviceIdUniqueness(vo, rowNumber, existingDeviceIds, errors);

        // 验证业务系统存在性
        validateBusinessSystemExists(vo, rowNumber, businessSystemNames, errors);

        // 验证资源池存在性
        validateResourcePoolExists(vo, rowNumber, resourcePoolNames, errors);

        return errors;
    }

    /**
     * 验证必填字段
     */
    private static void validateRequiredFields(PhysicalMachineImportVO vo, int rowNumber, List<ValidationError> errors) {
        if (StringUtils.isBlank(vo.getDeviceName())) {
            errors.add(new ValidationError(rowNumber, "物理机名称", "物理机名称不能为空", vo.getDeviceName()));
        }

        if (StringUtils.isBlank(vo.getGpuCardType())) {
            errors.add(new ValidationError(rowNumber, "显卡类型", "显卡类型不能为空", vo.getGpuCardType()));
        }

        if (StringUtils.isBlank(vo.getGpuType())) {
            errors.add(new ValidationError(rowNumber, "显卡型号", "显卡型号不能为空", vo.getGpuType()));
        }

        if (StringUtils.isBlank(vo.getGpuNum())) {
            errors.add(new ValidationError(rowNumber, "数量", "数量不能为空", vo.getGpuNum()));
        }

        if (StringUtils.isBlank(vo.getResourcePoolName())) {
            errors.add(new ValidationError(rowNumber, "资源池", "资源池不能为空", vo.getResourcePoolName()));
        }

        if (StringUtils.isBlank(vo.getResourceApplyTime())) {
            errors.add(new ValidationError(rowNumber, "开通时间", "开通时间不能为空", vo.getResourceApplyTime()));
        }

        if (StringUtils.isBlank(vo.getExpireTime())) {
            errors.add(new ValidationError(rowNumber, "到期时间", "到期时间不能为空", vo.getExpireTime()));
        }

        if (StringUtils.isBlank(vo.getApplyUserName())) {
            errors.add(new ValidationError(rowNumber, "申请人", "申请人不能为空", vo.getApplyUserName()));
        }

        if (StringUtils.isBlank(vo.getDeviceId())) {
            errors.add(new ValidationError(rowNumber, "配置项编号", "配置项编号不能为空", vo.getDeviceId()));
        }
    }

    /**
     * 验证枚举值
     */
    private static void validateEnumValues(PhysicalMachineImportVO vo, int rowNumber, List<ValidationError> errors) {
        // 验证交维状态
        if (StringUtils.isNotBlank(vo.getHandoverStatus())) {
            boolean validHandoverStatus = false;
            for (HAndoverStatusEnum status : HAndoverStatusEnum.values()) {
                if (status.getType().equals(vo.getHandoverStatus())) {
                    validHandoverStatus = true;
                    break;
                }
            }
            if (!validHandoverStatus) {
                errors.add(new ValidationError(rowNumber, "交维状态", "交维状态值无效，只能是：已交维、未交维", vo.getHandoverStatus()));
            }
        }

        // 验证申请时长
        if (StringUtils.isNotBlank(vo.getApplyTime())) {
            ApprovalTimeEnum approvalTime = ApprovalTimeEnum.getByName(vo.getApplyTime());
            if (approvalTime == ApprovalTimeEnum.DEFAULT) {
                errors.add(new ValidationError(rowNumber, "申请时长", "申请时长值无效，只能是：一个月、三个月、六个月、一年、两年", vo.getApplyTime()));
            }
        }
    }

    /**
     * 验证显卡类型
     */
    private static void validateGpuCardType(PhysicalMachineImportVO vo, int rowNumber, List<ValidationError> errors) {
        if (StringUtils.isNotBlank(vo.getGpuCardType())) {
            if (!"NPU".equals(vo.getGpuCardType()) && !"GPU".equals(vo.getGpuCardType())) {
                errors.add(new ValidationError(rowNumber, "显卡类型", "显卡类型只能填写NPU或GPU", vo.getGpuCardType()));
            }
        }
    }

    /**
     * 验证数量
     */
    private static void validateGpuNum(PhysicalMachineImportVO vo, int rowNumber, List<ValidationError> errors) {
        if (StringUtils.isNotBlank(vo.getGpuNum())) {
            try {
                int num = Integer.parseInt(vo.getGpuNum());
                if (num <= 0) {
                    errors.add(new ValidationError(rowNumber, "数量", "数量必须为正整数", vo.getGpuNum()));
                }
            } catch (NumberFormatException e) {
                errors.add(new ValidationError(rowNumber, "数量", "数量必须为正整数", vo.getGpuNum()));
            }
        }
    }

    /**
     * 验证硬盘字段
     */
    private static void validateDataDisk(PhysicalMachineImportVO vo, int rowNumber, List<ValidationError> errors) {
        if (StringUtils.isNotBlank(vo.getDataDisk())) {
            String dataDisk = vo.getDataDisk().trim();

            // 检查是否包含单位，如果包含则提示错误
            if (dataDisk.toLowerCase().endsWith("gb") || dataDisk.toLowerCase().endsWith("tb") ||
                    dataDisk.toLowerCase().endsWith("mb") || dataDisk.toLowerCase().endsWith("kb")) {
                errors.add(new ValidationError(rowNumber, "硬盘(GB)",
                        "硬盘字段请只填写数值，不要包含单位（如GB、TB等），系统会自动添加GB单位", dataDisk));
                return;
            }

            // 验证是否为有效的正数（可以包含小数点）
            try {
                double diskSize = Double.parseDouble(dataDisk);
                if (diskSize <= 0) {
                    errors.add(new ValidationError(rowNumber, "硬盘(GB)",
                            "硬盘容量必须为正数", dataDisk));
                }
            } catch (NumberFormatException e) {
                errors.add(new ValidationError(rowNumber, "硬盘(GB)",
                        "硬盘容量格式不正确，请填写有效的数值（如：96960）", dataDisk));
            }
        }
    }

    /**
     * 验证IP地址格式
     */
    private static void validateIpAddress(PhysicalMachineImportVO vo, int rowNumber, List<ValidationError> errors) {
        if (StringUtils.isNotBlank(vo.getIp())) {
            String ip = vo.getIp().trim();
            if (!IpUtils.isIPv4(ip) && !IpUtils.isIPv6(ip)) {
                errors.add(new ValidationError(rowNumber, "IP地址", "IP地址格式不正确", vo.getIp()));
            }
        }
    }

    /**
     * 验证日期格式
     */
    private static void validateDateFormats(PhysicalMachineImportVO vo, int rowNumber, List<ValidationError> errors) {
        // 验证开通时间
        if (StringUtils.isNotBlank(vo.getResourceApplyTime())) {
            try {
                DATE_FORMAT.parse(vo.getResourceApplyTime());
            } catch (ParseException e) {
                errors.add(new ValidationError(rowNumber, "开通时间", "开通时间格式不正确，应为：yyyy年M月d日", vo.getResourceApplyTime()));
            }
        }

        // 验证到期时间
        if (StringUtils.isNotBlank(vo.getExpireTime())) {
            try {
                DATE_FORMAT.parse(vo.getExpireTime());
            } catch (ParseException e) {
                errors.add(new ValidationError(rowNumber, "到期时间", "到期时间格式不正确，应为：yyyy年M月d日", vo.getExpireTime()));
            }
        }
    }

    /**
     * 验证设备ID唯一性
     */
    private static void validateDeviceIdUniqueness(PhysicalMachineImportVO vo, int rowNumber,
                                                   List<String> existingDeviceIds, List<ValidationError> errors) {
        if (StringUtils.isNotBlank(vo.getDeviceId()) && existingDeviceIds.contains(vo.getDeviceId())) {
            errors.add(new ValidationError(rowNumber, "配置项编号", "配置项编号已存在", vo.getDeviceId()));
        }
    }

    /**
     * 验证业务系统存在性
     */
    private static void validateBusinessSystemExists(PhysicalMachineImportVO vo, int rowNumber,
                                                     List<String> businessSystemNames, List<ValidationError> errors) {
        if (StringUtils.isNotBlank(vo.getBusinessSysName()) && !businessSystemNames.contains(vo.getBusinessSysName())) {
            errors.add(new ValidationError(rowNumber, "业务系统", "业务系统不存在", vo.getBusinessSysName()));
        }
    }

    /**
     * 验证资源池存在性
     */
    private static void validateResourcePoolExists(PhysicalMachineImportVO vo, int rowNumber,
                                                   List<String> resourcePoolNames, List<ValidationError> errors) {
        if (StringUtils.isNotBlank(vo.getResourcePoolName()) && !resourcePoolNames.contains(vo.getResourcePoolName())) {
            errors.add(new ValidationError(rowNumber, "资源池", "资源池不存在", vo.getResourcePoolName()));
        }
    }

    /**
     * 解析日期字符串为LocalDateTime对象
     *
     * @param dateStr 日期字符串，格式：yyyy年M月d日
     * @return LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            Date date = DATE_FORMAT.parse(dateStr);
            return date.toInstant()
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime();
        } catch (ParseException e) {
            log.warn("日期解析失败: {}", dateStr, e);
            return null;
        }
    }

    /**
     * 组合CPU和内存为SPEC字段
     *
     * @param cpu CPU核数
     * @param memory 内存GB
     * @return 组合后的SPEC字符串，格式："CPU核数|内存GB"
     */
    public static String combineSpec(String cpu, String memory) {
        if (StringUtils.isBlank(cpu) && StringUtils.isBlank(memory)) {
            return null;
        }

        String cpuPart = StringUtils.isBlank(cpu) ? "" : cpu;
        String memoryPart = StringUtils.isBlank(memory) ? "" : memory;

        return cpuPart + "|" + memoryPart;
    }

    /**
     * 格式化硬盘容量，自动添加GB单位
     *
     * @param dataDiskValue 硬盘容量数值
     * @return 格式化后的硬盘容量字符串，格式："数值GB"
     */
    public static String formatDataDisk(String dataDiskValue) {
        if (StringUtils.isBlank(dataDiskValue)) {
            return null;
        }

        String trimmedValue = dataDiskValue.trim();

        // 如果已经包含单位，直接返回（虽然验证阶段应该已经拦截了）
        if (trimmedValue.toLowerCase().endsWith("gb") ||
                trimmedValue.toLowerCase().endsWith("tb") ||
                trimmedValue.toLowerCase().endsWith("mb") ||
                trimmedValue.toLowerCase().endsWith("kb")) {
            return trimmedValue;
        }

        // 验证是否为有效数值
        try {
            Double.parseDouble(trimmedValue);
            return trimmedValue + "GB";
        } catch (NumberFormatException e) {
            log.warn("硬盘容量格式化失败，无效的数值: {}", trimmedValue);
            return trimmedValue; // 返回原值，让后续处理决定如何处理
        }
    }

    /**
     * 验证错误信息
     */
    public static class ValidationError {
        private int rowNumber;
        private String fieldName;
        private String errorMessage;
        private String errorValue;

        public ValidationError(int rowNumber, String fieldName, String errorMessage, String errorValue) {
            this.rowNumber = rowNumber;
            this.fieldName = fieldName;
            this.errorMessage = errorMessage;
            this.errorValue = errorValue;
        }

        // Getters
        public int getRowNumber() {
            return rowNumber;
        }

        public String getFieldName() {
            return fieldName;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getErrorValue() {
            return errorValue;
        }
    }
}
