package com.datatech.slgzt.model.vo.resource;

import lombok.Data;

/**
 * 物理机Excel导入VO
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
public class PhysicalMachineImportVO {

    /**
     * 物理机名称 - 必填
     */
    private String deviceName;

    /**
     * 交维状态 - 可选，需验证HandoverStatusEnum枚举值
     */
    private String handoverStatus;

    /**
     * 系统版本 - 可选
     */
    private String osVersion;

    /**
     * 显卡类型 - 必填，只能填写NPU或GPU
     */
    private String gpuCardType;

    /**
     * 显卡型号 - 必填
     */
    private String gpuType;

    /**
     * 数量 - 必填，必须为正整数
     */
    private String gpuNum;

    /**
     * CPU - 可选，与内存组合格式："CPU核数|内存GB"
     */
    private String cpu;

    /**
     * 内存(GB) - 可选，与CPU组合到SPEC字段
     */
    private String memory;

    /**
     * 硬盘 - 可选
     */
    private String dataDisk;

    /**
     * IP地址 - 可选，需验证IPv4/IPv6格式
     */
    private String ip;

    /**
     * 申请时长 - 可选，需验证ApprovalTimeEnum枚举值
     */
    private String applyTime;

    /**
     * 业务系统 - 可选，需查找数据库获取BUSINESS_SYS_ID
     */
    private String businessSysName;

    /**
     * 所属云 - 可选
     */
    private String cloudPlatform;

    /**
     * 资源池 - 必填，需查找数据库获取RESOURCE_POOL_ID
     */
    private String resourcePoolName;

    /**
     * 开通时间 - 必填，支持"yyyy年M月d日"格式解析
     */
    private String resourceApplyTime;

    /**
     * 到期时间 - 必填，支持"yyyy年M月d日"格式解析
     */
    private String expireTime;

    /**
     * 申请人 - 必填
     */
    private String applyUserName;

    /**
     * 配置项编号 - 必填，需验证唯一性
     */
    private String deviceId;
}
