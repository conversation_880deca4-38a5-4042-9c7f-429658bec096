package com.datatech.slgzt.model.vo.device;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月19日 15:58:38
 */
@Data
public class DevicePageVirtualVO {


    private String regionName;

    private String businessSystemName;

    private String deviceId;

    private String areaCode;

    //算力利用率
    private BigDecimal gpuUtilPercent;

    //显存利用率
    private BigDecimal memUtilpercent;

    //显存大小
    private BigDecimal memory;

    //任务数
    private Integer taskNum;

    private String  lastPeriod;

    

}
