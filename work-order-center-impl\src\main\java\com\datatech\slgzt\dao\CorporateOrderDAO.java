package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.CorporateOrderMapper;
import com.datatech.slgzt.dao.model.CorporateOrderDO;
import com.datatech.slgzt.model.query.CorporateOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class CorporateOrderDAO {

    @Resource
    private CorporateOrderMapper mapper;

    public List<CorporateOrderDO> list(CorporateOrderQuery query) {
        return mapper.selectList(
            Wrappers.<CorporateOrderDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getId()), CorporateOrderDO::getId, query.getId())
                .eq(ObjNullUtils.isNotNull(query.getOrderCode()), CorporateOrderDO::getOrderCode, query.getOrderCode())
                .eq(ObjNullUtils.isNotNull(query.getCreateBy()), CorporateOrderDO::getCreateBy, query.getCreateBy())
                .like(ObjNullUtils.isNotNull(query.getTenantName()), CorporateOrderDO::getTenantName, query.getTenantName())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeStart()), CorporateOrderDO::getCreateTime, query.getCreateTimeStart())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), CorporateOrderDO::getCreateTime, query.getCreateTimeEnd())
                .in(ObjNullUtils.isNotNull(query.getIds()), CorporateOrderDO::getId, query.getIds())
                .in(ObjNullUtils.isNotNull(query.getTenantIds()), CorporateOrderDO::getTenantId, query.getTenantIds())
                .orderByDesc(CorporateOrderDO::getCreateTime)
        );
    }

    public void insert(CorporateOrderDO corporateOrderDO) {
        mapper.insert(corporateOrderDO);
    }

    public void update(CorporateOrderDO corporateOrderDO) {
        mapper.updateById(corporateOrderDO);
    }

    public void delete(String id) {
        mapper.deleteById(id);
    }

    public CorporateOrderDO getById(String id) {
        return mapper.selectById(id);
    }

    public CorporateOrderDO getByOrderCode(String orderCode) {
        return mapper.selectOne(Wrappers.<CorporateOrderDO>lambdaQuery()
            .eq(CorporateOrderDO::getOrderCode, orderCode));
    }
} 