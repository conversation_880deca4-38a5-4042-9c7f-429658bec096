package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.VMResourcePerformanceManagerConvert;
import com.datatech.slgzt.dao.VMResourcePerformanceDAO;
import com.datatech.slgzt.manager.VMResourcePerformanceManager;
import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class VMResourcePerformanceManagerImpl implements VMResourcePerformanceManager {
    @Resource
    private VMResourcePerformanceDAO vmResourcePerformanceDAO;

    @Resource
    private VMResourcePerformanceManagerConvert vmResourcePerformanceManagerConvert;

    @Override
    public void insert(VMResourcePerformanceDTO dto) {
        vmResourcePerformanceDAO.insert(vmResourcePerformanceManagerConvert.dto2do(dto));
    }

    @Override
    public void update(VMResourcePerformanceDTO dto) {
        vmResourcePerformanceDAO.update(vmResourcePerformanceManagerConvert.dto2do(dto));
    }

    @Override
    public void delete(Long id) {
        vmResourcePerformanceDAO.delete(id);
    }

    @Override
    public VMResourcePerformanceDTO getById(Long resourceDetailId) {
        return vmResourcePerformanceManagerConvert.do2dto(vmResourcePerformanceDAO.getById(resourceDetailId));
    }
}