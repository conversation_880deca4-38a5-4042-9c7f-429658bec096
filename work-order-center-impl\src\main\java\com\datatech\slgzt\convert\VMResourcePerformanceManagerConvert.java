package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.VMResourcePerformanceDO;
import com.datatech.slgzt.model.dto.VMResourcePerformanceDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface VMResourcePerformanceManagerConvert {

    VMResourcePerformanceDTO do2dto(VMResourcePerformanceDO vmResourcePerformanceDO);

    VMResourcePerformanceDO dto2do(VMResourcePerformanceDTO vmResourcePerformanceDTO);
}