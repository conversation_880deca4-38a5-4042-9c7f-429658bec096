package com.datatech.slgzt.impl.service.corporate;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.xieyun.JobStepHelper;
import com.datatech.slgzt.manager.CorporateOrderProductManager;
import com.datatech.slgzt.model.dto.CorporateOrderProductDTO;
import com.datatech.slgzt.model.query.CorporateOrderProductQuery;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.corporate.CorporateResOpenService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.interceptor.DefaultTransactionAttribute;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月22日 10:23:50
 */
@Slf4j
@Configuration
public class CorporateCreateJobDef implements InitializingBean {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private List<CorporateResOpenService> corporateResOpenService;

    @Resource
    private CorporateCreateJobListener corporateCreateJobListener;

    private final Map<String, CorporateResOpenService> serviceMap = Maps.newHashMap();

    @Resource
    private CorporateOrderProductManager corporateOrderProductManager;

    @Resource
    private CmdbReportService cmdbReportService;


    @Bean("corporateProductCreateJob")
    public Job environmentCreationJob() {
        Job job = jobBuilderFactory.get("corporateProductCreateJob").incrementer(new RunIdIncrementer())
                .listener(corporateCreateJobListener)
                .start(corporateProductCreateInit())
                .next(corporateCloudPortCreate())
                .next(corporateEcsCreate())
                .next(corporateGcsCreate())
                .next(corporateMysqlCreate())
                .next(corporateSlbCreate())
                .next(corporateNatCreate())
                .next(corporateEipCreate())
                .next(corporateEvsCreate())
                .next(corporateObsCreate())
                .next(corporateVpnCreate())
                .next(corporateBackupCreate())
                .next(corporateCmdb())
                .build();
        return new ReferenceJobFactory(job).createJob();
    }

    //初始化的step用来获取执行id 主动停止 但是只会有一次
    @Bean("corporateProductCreateInit")
    public Step corporateProductCreateInit() {
        return stepBuilderFactory.get("corporateProductCreateInit").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String stepAlreadyExecuted = jobStepHelper.get("firstInit");
            //判断是否为空
            if (ObjNullUtils.isNull(stepAlreadyExecuted)) {
                //如果为空，说明是第一次执行
                jobStepHelper.put("firstInit", "true");
                //主动停止任务
                jobStepHelper.stop();
                return RepeatStatus.FINISHED;
            }
            return RepeatStatus.FINISHED;
        }).build();
    }

    @Bean("corporateEcsCreate")
    public Step corporateEcsCreate() {
        return stepBuilderFactory.get("corporateEcsCreate").tasklet(getProductTasklet(ProductTypeEnum.ECS)).build();
    }

    @Bean("corporateGcsCreate")
    public Step corporateGcsCreate() {
        return stepBuilderFactory.get("corporateGcsCreate").tasklet(getProductTasklet(ProductTypeEnum.GCS)).build();
    }

    @Bean("corporateMysqlCreate")
    public Step corporateMysqlCreate() {
        return stepBuilderFactory.get("corporateMysqlCreate").tasklet(getProductTasklet(ProductTypeEnum.RDS_MYSQL)).build();
    }

    @Bean("corporateSlbCreate")
    public Step corporateSlbCreate() {
        return stepBuilderFactory.get("corporateSlbCreate").tasklet(getProductTasklet(ProductTypeEnum.SLB)).build();
    }


    @Bean("corporateNatCreate")
    public Step corporateNatCreate() {
        return stepBuilderFactory.get("corporateNatCreate").tasklet(getProductTasklet(ProductTypeEnum.NAT)).build();
    }

    @Bean("corporateEipCreate")
    public Step corporateEipCreate() {
        return stepBuilderFactory.get("corporateEipCreate").tasklet(getProductTasklet(ProductTypeEnum.EIP)).build();
    }

    @Bean("corporateEvsCreate")
    public Step corporateEvsCreate() {
        return stepBuilderFactory.get("corporateEvsCreate").tasklet(getProductTasklet(ProductTypeEnum.EVS)).build();
    }

    @Bean("corporateObsCreate")
    public Step corporateObsCreate() {
        return stepBuilderFactory.get("corporateObsCreate").tasklet(getProductTasklet(ProductTypeEnum.OBS)).build();
    }

    @Bean("corporateVpnCreate")
    public Step corporateVpnCreate() {
        return stepBuilderFactory.get("corporateVpnCreate").tasklet(getProductTasklet(ProductTypeEnum.VPN)).build();
    }

    @Bean("corporateBackupCreate")
    public Step corporateBackupCreate() {
        return stepBuilderFactory.get("corporateBackupCreate").tasklet(getProductTasklet(ProductTypeEnum.BACKUP)).build();
    }

    @Bean("corporateCmdb")
    public Step corporateCmdb() {
        return stepBuilderFactory.get("corporateCmdb").tasklet((contribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // consumer回调会insert resource detail，
            // 它必然在layoutModify之前，layoutModify完事就重启
            Thread.sleep(40000);
            cmdbReportService.createInstanceOfCorporate(orderId);
            return RepeatStatus.FINISHED;
        }).build();
    }

    private Tasklet getProductTasklet(ProductTypeEnum productType) {
        return (contribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // 通过orderId获取到product对象
            List<CorporateOrderProductDTO> productDTOs = corporateOrderProductManager.list(new CorporateOrderProductQuery()
                    .setOrderId(orderId)
                    .setProductType(productType.getCode()));
            //如果productDTOs为空，说明没有相关的资源，直接返回
            if (ObjNullUtils.isNull(productDTOs)) {
                return RepeatStatus.FINISHED;
            }
            // 找一个product，进行开通
            Optional<CorporateOrderProductDTO> productDTOOptional = filterProduct(productDTOs);
            if (productDTOOptional.isPresent()) {
                CorporateResOpenService createService = serviceMap.get(productType.getCode());
                CorporateOrderProductDTO productDTO = productDTOOptional.get();
                try {
                    // 开通
                    createService.openResource(productDTO);
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", productDTO, ExceptionUtils.getStackTrace(e));
                    CorporateOrderProductDTO dto = new CorporateOrderProductDTO();
                    dto.setId(productDTO.getId());
                    dto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                    dto.setMessage(e.getMessage());
                    corporateOrderProductManager.update(dto);
                }
                // 开通后stop，等待product开通成功后，回调restart
                jobStepHelper.stop();
            }
            return RepeatStatus.FINISHED;
        };
    }

    // cloudPort创建
    @Bean("corporateCloudPortCreate")
    public Step corporateCloudPortCreate() {
        DefaultTransactionAttribute attribute = new DefaultTransactionAttribute();
        attribute.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);
        return stepBuilderFactory.get("corporateCloudPortCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            //通过orderId 获取到VPC创建的对象
            List<CorporateOrderProductDTO> cloudPortDTOList = corporateOrderProductManager.list(new CorporateOrderProductQuery()
                    .setOrderId(orderId)
                    .setProductType("cloudPort"));
            //如果ecsDTOList不为空，说明已经创建完ecs了，直接返回
            if (ObjNullUtils.isNull(cloudPortDTOList)) {
                return RepeatStatus.FINISHED;
            }
            cloudPortDTOList.forEach(corporateOrderProductDTO -> {
                try {
                    CorporateResOpenService createService = serviceMap.get("cloudPort");
                    createService.openResource(corporateOrderProductDTO);
                    //更新产品表
                    corporateOrderProductDTO.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
                    corporateOrderProductManager.update(corporateOrderProductDTO);
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", corporateOrderProductDTO, ExceptionUtils.getStackTrace(e));
                    //更新产品表
                    corporateOrderProductDTO.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                    corporateOrderProductDTO.setMessage(e.getMessage());
                    corporateOrderProductManager.update(corporateOrderProductDTO);
                    //已经这个节点关闭事务直接抛出异常即可
                    throw e;
                }

            });
            return RepeatStatus.FINISHED;
        }).transactionAttribute(attribute).build();
    }


    @Override
    public void afterPropertiesSet() {
        for (CorporateResOpenService service : corporateResOpenService) {
            serviceMap.put(service.registerOpenService().getCode(), service);
        }
    }

    private Optional<CorporateOrderProductDTO> filterProduct(List<CorporateOrderProductDTO> productDTOs) {
        return productDTOs.stream().filter(i -> i.getParentProductId() == 0
                && (ResOpenEnum.WAIT_OPEN.getCode().equals(i.getOpenStatus()) || ResOpenEnum.OPEN_FAIL.getCode().equals(i.getOpenStatus()))
        ).findFirst();
    }
}
