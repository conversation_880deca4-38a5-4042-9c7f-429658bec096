package com.datatech.slgzt.controller.zs;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.ZsMapWebConvert;
import com.datatech.slgzt.enums.DeviceMetricSourceEnum;
import com.datatech.slgzt.manager.DeviceCardMetricsManager;
import com.datatech.slgzt.manager.DeviceGpuInfoManager;
import com.datatech.slgzt.manager.DeviceVirtualInfoManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.query.DeviceInfoQuery;
import com.datatech.slgzt.model.query.DeviceMetricQuery;
import com.datatech.slgzt.model.req.device.DeviceDomainPageReq;
import com.datatech.slgzt.model.vo.device.*;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.base.Splitter;
import com.google.common.collect.ArrayListMultimap;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智算地图统一控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 06月16日 11:16:59
 */
@RestController
@RequestMapping("/zsMap")
public class ZsMapController {

    @Resource
    private DeviceGpuInfoManager deviceGupInfoManager;

    @Resource
    private DeviceCardMetricsManager deviceCardMetricsManager;

    @Resource
    private DeviceVirtualInfoManager deviceVirtualInfoManager;

    @Resource
    private ZsMapWebConvert zsMapWebConvert;


    /**
     * 物理卡分布情况
     */
    @PostMapping("/devicePhysicalTypeAreaGroup")
    public CommonResult<List<DevicePhysicalTypeAreaGroupVO>> devicePhysicalTypeAreaGroup(@RequestBody JSONObject req) {
        ArrayList<DevicePhysicalTypeAreaGroupVO> rList = Lists.newArrayList();
        //查询所有物理卡
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery());
        //先按照区划分组
        ArrayListMultimap<String, DeviceGpuInfoDTO> areaCode2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getAreaCode);
        for (String areaCode : areaCode2Data.keySet()) {
            DevicePhysicalTypeAreaGroupVO vo = new DevicePhysicalTypeAreaGroupVO();
            vo.setAreaCode(areaCode);
            vo.setModelName(areaCode2Data.get(areaCode).stream().map(DeviceGpuInfoDTO::getModelName).distinct().collect(Collectors.toList()));
            rList.add(vo);
        }
        return CommonResult.success(rList);

    }

    @PostMapping("/totalPhysicalSlice")
    public CommonResult<List<DeviceTotalPhysicalVO>> totalPhysicalSlice(@RequestBody DeviceDomainPageReq req) {
        //获取所有物理卡的信息
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setAreaCode(req.getAreaCode())
                .setSliceStatus("1") //只查询物理卡
        );
        //按照modelName分组
        ArrayListMultimap<String, DeviceGpuInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getModelName);
        List<DeviceTotalPhysicalVO> rList=new ArrayList<>();
        for (String modelname : modelName2Data.keySet()) {
            DeviceTotalPhysicalVO vo = new DeviceTotalPhysicalVO();
            List<DeviceGpuInfoDTO> deviceGpuInfoDTOS1 = modelName2Data.get(modelname);
            //把设备id整理出来
            List<String> deviceIdList = deviceGpuInfoDTOS1.stream()
                                                          .map(DeviceGpuInfoDTO::getDeviceId)
                                                          .filter(ObjNullUtils::isNotNull)
                                                          .distinct()
                                                          .collect(Collectors.toList());
            vo.setModelName(modelname);
            vo.setTotalCount(deviceGpuInfoDTOS1.size());
            List<DeviceVirtualInfoDTO> deviceVirtualInfoDTOS = deviceVirtualInfoManager.selectDeviceVirtualInfoList(new DeviceInfoQuery()
                    .setPhysicalDeviceIdList(deviceIdList)
                    .setAreaCode(req.getAreaCode())
            );
            vo.setAllocatedCount(deviceVirtualInfoDTOS.size());
            rList.add(vo);
        }
        return CommonResult.success(rList);
    }


    /**
     * 获取数量详情按照类型分组
     *
     * @param query
     * @return
     */
    @PostMapping("/totalPhysical")
    public CommonResult<List<DeviceTotalPhysicalVO>> queryDeviceComputeMetricPage(@RequestBody DeviceDomainPageReq deviceDomainPageReq) {
        //获取所有物理卡的信息
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setAreaCode(deviceDomainPageReq.getAreaCode())
        );
        //dataList还需按照类型再次分组
        ArrayListMultimap<String, DeviceGpuInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getModelName);
        List<DeviceTotalPhysicalVO> modelList = Lists.newArrayList();
        Set<String> modelNameSet = modelName2Data.keySet();
        for (String modelName : modelNameSet) {
            DeviceTotalPhysicalVO model = new DeviceTotalPhysicalVO();
            List<DeviceGpuInfoDTO> dataList1 = modelName2Data.get(modelName);
            int totalCount = dataList1.size();
            long inUsedCount = dataList1.stream()
                                        .filter(i -> i.getInUsed().equalsIgnoreCase("1"))
                                        .count();
            model.setModelName(modelName);
            model.setTotalCount(totalCount);
            model.setAllocatedCount((int) inUsedCount);
            model.setAvailableCount(totalCount - (int) inUsedCount);
            modelList.add(model);
        }
        return CommonResult.success(modelList);
    }


    /**
     * 总算力详情 按照类型分组的
     *
     * @return
     */
    @PostMapping("/totalCompute")
    public CommonResult<DeviceComputeBaseInfoVO> totalCompute(@RequestBody DeviceInfoQuery query) {
        DeviceComputeBaseInfoVO resultVO = new DeviceComputeBaseInfoVO();
        List<DeviceComputeBaseInfoVO.Model> rModelList = Lists.newArrayList();
        //获取所有物理卡的信息
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setAreaCode(query.getAreaCode())
        );
        //按照显卡类型区分
        ArrayListMultimap<String, DeviceGpuInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getModelName);
        //区分出每个类型下的分配数
        Set<String> modelNameSet = modelName2Data.keySet();
        for (String modelName : modelNameSet) {
            DeviceComputeBaseInfoVO.Model model = new DeviceComputeBaseInfoVO.Model();
            List<DeviceGpuInfoDTO> dataList = modelName2Data.get(modelName);
            int totalCount = dataList.size();
            long inUsedCount = dataList.stream()
                                       .filter(i -> i.getInUsed().equalsIgnoreCase("1"))
                                       .count();
            model.setModelName(modelName);
            model.setTotalCount(totalCount);
            model.setAllocatedCount((int) inUsedCount);
            model.setAvailableCount(totalCount - (int) inUsedCount);
            rModelList.add(model);
        }
        resultVO.setModelList(rModelList);
        //获取所有的指标信息
        return CommonResult.success(resultVO);
    }


    /**
     * 获取算力业务排序对象
     *
     * @param query
     * @return
     */
    @PostMapping("/queryDeviceRunSort")
    public CommonResult<List<DeviceRunSortVO>> queryDeviceRunSort(@RequestBody DeviceInfoQuery query) {
        //查询所有已经有数据的物理卡
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setAreaCode(query.getAreaCode())
                .setGpuSort(true));
        //按照模块类型分组
        ArrayListMultimap<String, DeviceGpuInfoDTO> modelName2Data = StreamUtils.toArrayListMultimap(deviceGpuInfoDTOS, DeviceGpuInfoDTO::getModelName);
        List<DeviceRunSortVO> resultList = Lists.newArrayList();
        Set<String> modelNameSet = modelName2Data.keySet();
        for (String modelName : modelNameSet) {
            DeviceRunSortVO deviceRunSortVO = new DeviceRunSortVO();
            deviceRunSortVO.setModelName(modelName);
            List<DeviceGpuInfoDTO> dataList = modelName2Data.get(modelName);
            //提取出DeviceGpuInfoDTO 里的lastPeriod字段 转成DeviceCardMetricsDTO 列表
            //取出第一个条 如果存在的话
            List<DeviceCardMetricsDTO> deviceCardMetricsDTOS = dataList.stream()
                                                                       .map(DeviceGpuInfoDTO::getLastPeriod)
                                                                       .filter(ObjNullUtils::isNotNull)
                                                                       .map(i -> JSONObject.parseObject(i, DeviceCardMetricsDTO.class))
                                                                       .filter(i -> ObjNullUtils.isNotNull(i.getGpuUtilPercent()))
                                                                       .sorted(Comparator.comparing(DeviceCardMetricsDTO::getGpuUtilPercent)
                                                                                         .thenComparing(DeviceCardMetricsDTO::getMemUtilpercent)
                                                                                         .reversed())
                                                                       .collect(Collectors.toList());
            //按照显存利用率排序
            DeviceCardMetricsDTO deviceCardMetricsDTO = deviceCardMetricsDTOS.get(0);
            deviceRunSortVO.setMemUtilpercent(BigDecimal.valueOf(deviceCardMetricsDTO.getMemUtilpercent())
                                                        .setScale(2, RoundingMode.HALF_UP));
            deviceRunSortVO.setGpuUtilPercent(BigDecimal.valueOf(deviceCardMetricsDTO.getGpuUtilPercent())
                                                        .setScale(2, RoundingMode.HALF_UP));
            DeviceGpuInfoDTO deviceGpuInfoDTO = deviceGupInfoManager.getByDeviceId(deviceCardMetricsDTO.getDeviceId());
            deviceRunSortVO.setBusinessName(deviceGpuInfoDTO.getBusinessSystemName());
            resultList.add(deviceRunSortVO);
        }
        //获取
        List<String> modelNameList = deviceGupInfoManager.groupModelName();
        //循环modelNameSet 如果没有给个默认的对象补齐
        for (String modelName : modelNameList) {
            if (!modelNameSet.contains(modelName)) {
                DeviceRunSortVO deviceRunSortVO = new DeviceRunSortVO();
                deviceRunSortVO.setModelName(modelName);
                deviceRunSortVO.setMemUtilpercent(null);
                deviceRunSortVO.setGpuUtilPercent(null);
                List<DeviceGpuInfoDTO> deviceGpuInfoDTOS1 = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                        .setModelName(modelName)
                        .setInUsed("1")
                        .setAreaCode(query.getAreaCode()));
                DeviceGpuInfoDTO any = StreamUtils.findAny(deviceGpuInfoDTOS1, DeviceGpuInfoDTO::new);
                deviceRunSortVO.setBusinessName(any.getBusinessSystemName());
                resultList.add(deviceRunSortVO);
            }
        }
        return CommonResult.success(resultList);
    }


    /**
     * 智算显存、算力利用率 按照区划归类
     */
    @PostMapping("/queryDevicesMetricPercent")
    public CommonResult<List<DevicePhysicalRunStatusVO>> queryDevicesMetricPercent(@RequestBody DeviceInfoQuery query) {
        //查询对象按照4个小时 区域分好
        List<DeviceCardMetricsDTO> deviceCardMetricsDTOS = deviceCardMetricsManager.queryAvgDeviceMetrics(new DeviceMetricQuery()
                .setStartTime(LocalDateTime.now().minusHours(4))
                .setEndTime(LocalDateTime.now())
                .setAreaCode(query.getAreaCode())
                .setMetricSources(Arrays.asList(DeviceMetricSourceEnum.QD_PHYSICAL_SOURCE.getCode(), DeviceMetricSourceEnum.NPU_CHIP_SOURCE.getCode()))
        );
        //把区划空的过滤掉
        deviceCardMetricsDTOS = deviceCardMetricsDTOS.stream()
                                                     .filter(i -> ObjNullUtils.isNotNull(i.getAreaCode()))
                                                     .collect(Collectors.toList());
        //按照区分分组
        ArrayListMultimap<String, DeviceCardMetricsDTO> arrayListMultimap = StreamUtils.toArrayListMultimap(deviceCardMetricsDTOS, DeviceCardMetricsDTO::getAreaCode);
        ArrayList<DevicePhysicalRunStatusVO> resultList = Lists.newArrayList();
        Set<String> areaCodeSet = arrayListMultimap.keySet();
        for (String areaCode : areaCodeSet) {
            //这里取出来的包含这个区划下所有卡的指标
            List<DeviceCardMetricsDTO> metricsDTOS = arrayListMultimap.get(areaCode);
            //需要按照时间来划开 按照体统的GPUTime来分组计算
            ArrayListMultimap<String, DeviceCardMetricsDTO> gpuTime2Data = StreamUtils.toArrayListMultimap(metricsDTOS, DeviceCardMetricsDTO::getGpuTime);
            //计算平均的时间点
            DevicePhysicalRunStatusVO devicePhysicalRunStatusVO = new DevicePhysicalRunStatusVO();
            devicePhysicalRunStatusVO.setAreaCode(areaCode);
            List<DevicePhysicalRunStatusVO.Model> modelList = Lists.newArrayList();
            //计算每个时间点的平均值
            gpuTime2Data.keySet().forEach(gpuTime -> {
                List<DeviceCardMetricsDTO> deviceCardMetricsDTOS1 = gpuTime2Data.get(gpuTime);
                //计算每个时间点的平均值
                DevicePhysicalRunStatusVO.Model model = new DevicePhysicalRunStatusVO.Model();
                //计算平均算力利用率
                double gpuUtilPercent = deviceCardMetricsDTOS1.stream()
                                                              .mapToDouble(DeviceCardMetricsDTO::getGpuUtilPercent)
                                                              .average()
                                                              .orElse(0);
                //计算平均显存利用率
                double memUtilpercent = deviceCardMetricsDTOS1.stream()
                                                              .mapToDouble(DeviceCardMetricsDTO::getMemUtilpercent)
                                                              .average()
                                                              .orElse(0);
                //计算GPUTime对应的时间
                model.setTime(DateUtils.toLocalDateTime(gpuTime, "yyyyMMddHHmm"));
                model.setGpuUtilPercent(BigDecimal.valueOf(gpuUtilPercent).setScale(2, RoundingMode.HALF_UP));
                model.setMemUtilpercent(BigDecimal.valueOf(memUtilpercent).setScale(2, RoundingMode.HALF_UP));
                modelList.add(model);
            });
            //modeList需要按照时间排序
            modelList.sort(Comparator.comparing(DevicePhysicalRunStatusVO.Model::getTime));
            devicePhysicalRunStatusVO.setModelList(modelList);
            resultList.add(devicePhysicalRunStatusVO);
        }
        return CommonResult.success(resultList);
    }


    //业务查询物理卡列表数据
    @PostMapping("/pageDeviceGpuInfoList")
    public CommonResult<PageResult<DevicePagePhysicalVO>> pageDeviceGpuInfoList(@RequestBody DeviceInfoQuery query) {
        PageResult<DeviceGpuInfoDTO> page = deviceGupInfoManager.queryDeviceGupInfoPage(new DeviceInfoQuery()
                .setPageNum(query.getPageNum())
                .setGpuSort(true)
                .setPageSize(query.getPageSize())
                .setModelName(query.getModelName())
        );
        PageResult<DevicePagePhysicalVO> box = PageWarppers.box(page, zsMapWebConvert::convert);
        List<DevicePagePhysicalVO> records = box.getRecords();
        records.forEach(i -> {
            //查询当前时间的数据
            String lastPeriod = i.getLastPeriod();
            if (ObjNullUtils.isNull(lastPeriod)) {
                return;
            }
            //按照创建时间排序
            DeviceCardMetricsDTO deviceCardMetricsDTO  = JSONObject.parseObject(lastPeriod, DeviceCardMetricsDTO.class);
            i.setGpuUtilPercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getGpuUtilPercent())?null:BigDecimal.valueOf(deviceCardMetricsDTO.getGpuUtilPercent())
                                          .setScale(2, RoundingMode.HALF_UP));
            i.setMemUtilpercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getMemUtilpercent())?null:BigDecimal.valueOf(deviceCardMetricsDTO.getMemUtilpercent())
                                          .setScale(2, RoundingMode.HALF_UP));
            i.setTaskNum(deviceCardMetricsDTO.getAllocationCount());
            i.setTemperature(ObjNullUtils.isNull(deviceCardMetricsDTO.getDevGpuTemp())?null:BigDecimal.valueOf(deviceCardMetricsDTO.getDevGpuTemp())
                                       .setScale(2, RoundingMode.HALF_UP));
        });
        return CommonResult.success(box);
    }

    //查询数据指标列表
    @PostMapping("/deviceMetricsList")
    public CommonResult<Map<String, List<DeviceMetricsListVO>>> deviceMetricsList(@RequestBody DeviceInfoQuery query) {
        //检查开始时间和结束时间不能为空
        //如果开始时间为空强制是指为最近4个小时
        if (ObjNullUtils.isNull(query.getStartTime())) {
            query.setStartTime(LocalDateTime.now().minusHours(4));
            query.setEndTime(LocalDateTime.now());
        }
        Precondition.checkArgument(query.getDeviceId(), "deviceId不能为空");
        //设备id逗号拆分分批查询
        List<String> deviceIdList = Arrays.stream(query.getDeviceId().split(","))
                                          .map(String::trim)
                                          .collect(Collectors.toList());
        Map<String, List<DeviceMetricsListVO>> deviceId2Data = new HashMap<>();
        deviceIdList.forEach(i -> {
                    List<DeviceCardMetricsDTO> deviceCardMetricsDTOS = deviceCardMetricsManager.selectGpuMetricsDTO(new DeviceMetricQuery()
                            .setStartTime(query.getStartTime())
                            .setEndTime(query.getEndTime())
                            .setDeviceId(i)
                    );
            List<DeviceMetricsListVO> deviceMetricsListVOS = StreamUtils.mapArray(deviceCardMetricsDTOS, zsMapWebConvert::convert);
            //按照gpuTime排序
            deviceMetricsListVOS.sort(Comparator.comparing(DeviceMetricsListVO::getGpuTime));
            deviceId2Data.put(i, deviceMetricsListVOS);
        });
        return CommonResult.success(deviceId2Data);
    }


    //分页查询虚拟卡
    @PostMapping("/pageDeviceVirtualGpuInfoList")
    public CommonResult<PageResult<DevicePageVirtualVO>> pageDeviceVirtualGpuInfoList(@RequestBody DeviceInfoQuery query) {
        PageResult<DeviceVirtualInfoDTO> page = deviceVirtualInfoManager.queryVirtualDeviceInfoPage(new DeviceInfoQuery()
                .setPageNum(query.getPageNum())
                .setPageSize(query.getPageSize())
                .setGpuSort(true)
                .setModelName(query.getModelName())
        );
        PageResult<DevicePageVirtualVO> box = PageWarppers.box(page, zsMapWebConvert::convert);
        List<DevicePageVirtualVO> records = box.getRecords();
        records.forEach(i -> {
            //查询当前时间的数据
            String lastPeriod = i.getLastPeriod();
            if (ObjNullUtils.isNull(lastPeriod)) {
                return;
            }
            i.setBusinessSystemName("智能视频");
            //按照创建时间排序
            DeviceCardMetricsDTO deviceCardMetricsDTO  = JSONObject.parseObject(lastPeriod, DeviceCardMetricsDTO.class);
            i.setGpuUtilPercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getGpuUtilPercent())?null:BigDecimal.valueOf(deviceCardMetricsDTO.getGpuUtilPercent())
                                          .setScale(2, RoundingMode.HALF_UP));
            i.setMemUtilpercent(ObjNullUtils.isNull(deviceCardMetricsDTO.getGpuUtilPercent())?null:BigDecimal.valueOf(deviceCardMetricsDTO.getMemUtilpercent())
                                          .setScale(2, RoundingMode.HALF_UP));
            i.setTaskNum(deviceCardMetricsDTO.getAllocationCount());
        });
        return CommonResult.success(box);
    }


    /**
     * 通过Ip查询指标数据
     */
    @PostMapping("/queryDeviceMetricsByIp")
    public CommonResult<Map<String, List<DeviceMetricsListVO>>> queryDeviceMetricsByIp(@RequestBody DeviceInfoQuery query) {
        Precondition.checkArgument(query.getDncIp());
        List<DeviceGpuInfoDTO> deviceGpuInfoDTOS = deviceGupInfoManager.selectDeviceGpuInfoList(new DeviceInfoQuery()
                .setDncIp(query.getDncIp())
        );
        //过滤掉deviceId为空
        deviceGpuInfoDTOS = deviceGpuInfoDTOS.stream()
                                             .filter(Objects::nonNull)
                                             .filter(i -> ObjNullUtils.isNotNull(i.getDeviceId()))
                                             .collect(Collectors.toList());
        if (ObjNullUtils.isNull(deviceGpuInfoDTOS)) {
            return CommonResult.success(Collections.emptyMap());
        }
        List<String> deviceIdList=new ArrayList<>();
        for (DeviceGpuInfoDTO deviceGpuInfoDTO : deviceGpuInfoDTOS) {
            String deviceId = deviceGpuInfoDTO.getDeviceId();
            deviceIdList.addAll(Splitter.on(",").splitToList(deviceId));
        }
        if (ObjNullUtils.isNull(query.getStartTime())) {
            query.setStartTime(LocalDateTime.now().minusHours(4));
            query.setEndTime(LocalDateTime.now());
        }
        Map<String, List<DeviceMetricsListVO>> deviceId2Data = new HashMap<>();
        deviceIdList.forEach(i -> {
            List<DeviceCardMetricsDTO> deviceCardMetricsDTOS = deviceCardMetricsManager.selectGpuMetricsDTO(new DeviceMetricQuery()
                    .setStartTime(query.getStartTime())
                    .setEndTime(query.getEndTime())
                    .setDeviceId(i)
            );
            List<DeviceMetricsListVO> deviceMetricsListVOS = StreamUtils.mapArray(deviceCardMetricsDTOS, zsMapWebConvert::convert);
            deviceMetricsListVOS.sort(Comparator.comparing(DeviceMetricsListVO::getGpuTime));
            deviceId2Data.put(i, deviceMetricsListVOS);
        });
        return CommonResult.success(deviceId2Data);

    }

}
