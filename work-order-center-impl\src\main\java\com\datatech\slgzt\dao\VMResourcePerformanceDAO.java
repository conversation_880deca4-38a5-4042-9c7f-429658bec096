package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.VMResourcePerformanceMapper;
import com.datatech.slgzt.dao.model.VMResourcePerformanceDO;
import com.datatech.slgzt.model.query.VMResourcePerformanceQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 虚拟机性能数据表DAO
 * <AUTHOR>
 */
@Repository
public class VMResourcePerformanceDAO {

    @Resource
    private VMResourcePerformanceMapper vmResourcePerformanceMapper;

    public Long insert(VMResourcePerformanceDO vmResourcePerformanceDO) {
        vmResourcePerformanceMapper.insert(vmResourcePerformanceDO);
        return vmResourcePerformanceDO.getResourceDetailId();
    }

    public void update(VMResourcePerformanceDO vmResourcePerformanceDO) {
        vmResourcePerformanceMapper.updateById(vmResourcePerformanceDO);
    }

    public void delete(Long resourceDetailId) {
        vmResourcePerformanceMapper.deleteById(resourceDetailId);
    }

    public VMResourcePerformanceDO getById(Long resourceDetailId) {
        return vmResourcePerformanceMapper.selectById(resourceDetailId);
    }

    public List<VMResourcePerformanceDO> list(VMResourcePerformanceQuery query) {
        return vmResourcePerformanceMapper.selectList(Wrappers.<VMResourcePerformanceDO>lambdaQuery()
                        .eq(ObjNullUtils.isNotNull(query.getResourceDetailId()), VMResourcePerformanceDO::getResourceDetailId, query.getResourceDetailId())
                        .eq(ObjNullUtils.isNotNull(query.getDeviceId()), VMResourcePerformanceDO::getDeviceId, query.getDeviceId())
                        .eq(ObjNullUtils.isNotNull(query.getDeviceName()), VMResourcePerformanceDO::getDeviceName, query.getDeviceName())
                        .eq(ObjNullUtils.isNotNull(query.getResourceDetailType()), VMResourcePerformanceDO::getResourceDetailType, query.getResourceDetailType())
                        .eq(ObjNullUtils.isNotNull(query.getDomainName()), VMResourcePerformanceDO::getDomainName, query.getDomainName())
                        .eq(ObjNullUtils.isNotNull(query.getDomainCode()), VMResourcePerformanceDO::getDomainCode, query.getDomainCode())
                        .eq(ObjNullUtils.isNotNull(query.getAzName()), VMResourcePerformanceDO::getAzName, query.getAzName())
                        .eq(ObjNullUtils.isNotNull(query.getAzCode()), VMResourcePerformanceDO::getAzCode, query.getAzCode())
//                .eq(ObjNullUtils.isNotNull(query.getVmInstanceUuid()), VMResourcePerformanceDO::getVmInstanceUuid, query.getVmInstanceUuid())
//                .eq(ObjNullUtils.isNotNull(query.getVmDeleted()), VMResourcePerformanceDO::getVmDeleted, query.getVmDeleted())
                        .eq(ObjNullUtils.isNotNull(query.getCkHostName()), VMResourcePerformanceDO::getCkHostName, query.getCkHostName())
                        .eq(ObjNullUtils.isNotNull(query.getCkIp()), VMResourcePerformanceDO::getCkIp, query.getCkIp())
//                .ge(ObjNullUtils.isNotNull(query.getCkLastedTimeStart()), VMResourcePerformanceDO::getCkLastedTime, query.getCkLastedTimeStart())
//                .le(ObjNullUtils.isNotNull(query.getCkLastedTimeEnd()), VMResourcePerformanceDO::getCkLastedTime, query.getCkLastedTimeEnd())

        );
    }
}