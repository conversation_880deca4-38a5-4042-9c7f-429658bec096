package com.datatech.slgzt.impl.service.standard;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.config.FiProperties;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.EsModel;
import com.datatech.slgzt.model.nostander.KafkaModel;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: workordercenterproject
 * @description: es开通（创建索引模板）
 * @author: LK
 * @create: 2025-06-23 14:34
 **/
@Service
@Slf4j
public class StandardResEsOpenServiceImpl implements StandardResOpenService {

    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Resource
    private StandardWorkOrderProductManager productManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private FiProperties fiProperties;

    @SneakyThrows
    @Override
    public void openStandardResource(StandardWorkOrderProductDTO productDTO) {
        EsModel esModel = JSON.parseObject(productDTO.getPropertySnapshot(), EsModel.class);
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("index_patterns", esModel.getIndexPatterns());
//        Map<String, Object> settingsMap = new HashMap<>();
//        Map<String, Object> indexMap = new HashMap<>();
//        indexMap.put("number_of_shards", esModel.getNumberOfShards());
//        indexMap.put("number_of_replicas", esModel.getNumberOfReplicas());
//        settingsMap.put("index", indexMap);
//        paramMap.put("settings", settingsMap);
//        Map<String, Object> propMap = new HashMap<>();
//        Map<String, Object> filedMap = new HashMap<>();
//        for (EsModel.FieldType fieldType : esModel.getFieldTypeList()) {
//            Map<String, Object> typeMap = new HashMap<>();
//            typeMap.put("type", fieldType.getType());
//            filedMap.put(fieldType.getFieldName(), typeMap);
//        }
//        propMap.put("properties", filedMap);
//        paramMap.put("mappings", propMap);
        // 更新产品状态为开通中
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        Mapper dataMapper = OkHttpsUtils.httpIgnoreHttpsCertificate(120)
                .sync(fiProperties.getRegionCode2EsCreateUrlMap().get(esModel.getRegionCode()) + esModel.getName())
                .bodyType(OkHttps.JSON)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization",
                        "Basic " + Base64.getEncoder().encodeToString((fiProperties.getUsername() + ":" + fiProperties.getPassword()).getBytes()))
                .setBodyPara(esModel.getIndexTemplate())
                .put()
                .getBody()
                .toMapper();
        log.info("响应数据：" + dataMapper.toString());
        boolean flag = dataMapper.getBool("acknowledged");
        if (!flag) {
            log.error("es资源开通失败");
            StandardWorkOrderProductDTO updateDto = new StandardWorkOrderProductDTO();
            updateDto.setId(productDTO.getId());
            updateDto.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
            updateDto.setMessage(dataMapper.getString("error"));
            productManager.update(updateDto);
            productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_FAIL.getCode());
        } else {
            productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
            productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
            //产品入库操作
            saveResource(productDTO.getWorkOrderId(), esModel);
        }
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.ES;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }

    private void saveResource(String workOrderId, EsModel esModel) {
        StandardWorkOrderDTO standardWorkOrderDTO = standardWorkOrderManager.getById(workOrderId);
        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setType(ProductTypeEnum.ES.getCode());
        resourceDetailDTO.setOrderId(standardWorkOrderDTO.getId());
        resourceDetailDTO.setOrderCode(standardWorkOrderDTO.getOrderCode());
        resourceDetailDTO.setBillId(standardWorkOrderDTO.getBillId());
        resourceDetailDTO.setDeviceId(IdUtil.simpleUUID());
        resourceDetailDTO.setDeviceName(esModel.getName());
        resourceDetailDTO.setTenantId(standardWorkOrderDTO.getTenantId());
        resourceDetailDTO.setTenantName(standardWorkOrderDTO.getTenantName());
        resourceDetailDTO.setBusinessSysId(standardWorkOrderDTO.getBusiSystemId());
        resourceDetailDTO.setBusinessSysName(standardWorkOrderDTO.getBusinessSystemName());
        resourceDetailDTO.setDomainCode(standardWorkOrderDTO.getDomainCode());
        resourceDetailDTO.setDomainName(standardWorkOrderDTO.getDomainName());
        resourceDetailDTO.setApplyUserName(standardWorkOrderDTO.getCreatedUserName());
        resourceDetailDTO.setResourcePoolId(String.valueOf(esModel.getRegionId()));
        resourceDetailDTO.setResourcePoolCode(esModel.getRegionCode());
        resourceDetailDTO.setResourcePoolName(esModel.getRegionName());
        //索引模板存到网络json字段里
        resourceDetailDTO.setNetworkModelSnapshot(JSON.toJSONString(esModel.getIndexTemplate()));
        //日均增量数据存到规格字段里
        resourceDetailDTO.setSpec(esModel.getAverageDailyIncrementData() + "GB/天");
        //保留时间存到频率字段里
        resourceDetailDTO.setFrequency(esModel.getRetainTime() + "天");
        //副本存到设备状态字段里
        resourceDetailDTO.setDeviceStatus(String.valueOf(esModel.getNumberOfReplicas()));
        //磁盘大小存到容量字段里
        resourceDetailDTO.setCapacity(esModel.getDiskSize() + "GB");
        resourceDetailDTO.setCreateTime(LocalDateTime.now());
        resourceDetailDTO.setResourceApplyTime(standardWorkOrderDTO.getCreateTime());
        resourceDetailDTO.setApplyTime(esModel.getApplyTime());
        resourceDetailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), esModel.getApplyTime()));
        resourceDetailDTO.setEffectiveTime(LocalDateTime.now());
        resourceDetailManager.batchSaveResourceDetail(ListUtil.toList(resourceDetailDTO));
    }
}
