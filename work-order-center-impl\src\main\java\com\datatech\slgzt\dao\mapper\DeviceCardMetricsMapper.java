package com.datatech.slgzt.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datatech.slgzt.dao.DeviceCardMetricsDAO;
import com.datatech.slgzt.dao.model.DeviceCardMetricsDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.time.LocalDateTime;
import java.util.List;


public interface DeviceCardMetricsMapper extends BaseMapper<DeviceCardMetricsDO> {


    @SelectProvider(type = DeviceCardMetricsMapper.class, method = "avgBuildQuery")
    List<DeviceCardMetricsDO> queryAvgDeviceMetrics(
            @Param("areaCode") String areaCode,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );


    // 动态 SQL 构建方法
    static String avgBuildQuery(@Param("areaCode") String areaCode,
                             @Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime) {
        return new org.apache.ibatis.jdbc.SQL() {{
            SELECT("AREA_CODE AS areaCode");
            SELECT("GPU_TIME AS gpuTime");
            SELECT("AVG(GPU_UTIL_PERCENT) AS GPU_UTIL_PERCENT");
            SELECT("AVG(MEM_UTIL_PERCENT) AS MEM_UTIL_PERCENT");

            FROM("woc_gpu_device_metrics_distributed");

            if (areaCode != null && !areaCode.isEmpty()) {
                WHERE("AREA_CODE = #{areaCode}");
            }
            if (startTime != null && endTime != null) {
                WHERE("CREATED_AT BETWEEN #{startTime} AND #{endTime}");
            }

            GROUP_BY("AREA_CODE, GPU_TIME");
            ORDER_BY("AREA_CODE, GPU_TIME");
        }}.toString();
    }
}
