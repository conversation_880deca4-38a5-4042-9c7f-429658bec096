package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.DeviceCardMetricsDTO;
import com.datatech.slgzt.model.dto.DeviceGpuInfoDTO;
import com.datatech.slgzt.model.dto.DeviceVirtualInfoDTO;
import com.datatech.slgzt.model.vo.device.DeviceCardMetricsVO;
import com.datatech.slgzt.model.vo.device.DeviceMetricsListVO;
import com.datatech.slgzt.model.vo.device.DevicePagePhysicalVO;
import com.datatech.slgzt.model.vo.device.DevicePageVirtualVO;
import com.datatech.slgzt.utils.DateUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Mapper(componentModel = "spring")
public interface ZsMapWebConvert {

    DevicePagePhysicalVO convert(DeviceGpuInfoDTO deviceGpuInfoDTO);

    DevicePageVirtualVO convert(DeviceVirtualInfoDTO deviceGpuInfoDTO);


    @Named("dddd")
    default DeviceMetricsListVO convert(DeviceCardMetricsDTO deviceGpuInfoDTO){
        DeviceMetricsListVO deviceCardMetricsVO = new DeviceMetricsListVO();
        deviceCardMetricsVO.setGpuUtilPercent(BigDecimal.valueOf(deviceGpuInfoDTO.getGpuUtilPercent()).setScale(2, RoundingMode.HALF_UP));
        deviceCardMetricsVO.setMemUtilpercent(BigDecimal.valueOf(deviceGpuInfoDTO.getMemUtilpercent()).setScale(2, RoundingMode.HALF_UP));
        deviceCardMetricsVO.setGpuTime(deviceGpuInfoDTO.getCreatedAt());
        deviceCardMetricsVO.setCreatedAt(deviceGpuInfoDTO.getCreatedAt());
        deviceCardMetricsVO.setDeviceId(deviceGpuInfoDTO.getDeviceId());
        return deviceCardMetricsVO;
    }
}
