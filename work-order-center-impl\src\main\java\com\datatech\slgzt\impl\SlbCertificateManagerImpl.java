package com.datatech.slgzt.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.convert.SlbCertificateManagerConvert;
import com.datatech.slgzt.dao.SlbCertificateDAO;
import com.datatech.slgzt.dao.mapper.business.BusinessMapper;
import com.datatech.slgzt.dao.model.SlbCertificateDO;
import com.datatech.slgzt.dao.model.db.CmpAppDO;
import com.datatech.slgzt.enums.BusinessExceptionEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.SlbCertificateManager;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.TaskStatusExt;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.query.SlbCertificateQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * SLB证书Manager实现
 */
@Slf4j
@Service
public class SlbCertificateManagerImpl implements SlbCertificateManager {

    @Resource
    private SlbCertificateDAO slbCertificateDAO;

    @Resource
    private SlbCertificateManagerConvert slbCertificateManagerConvert;

    @Value("${http.resourceCenterUrl}")
    private String resourceCenterApiUrl;

    @Resource
    private TenantManager tenantManager;
    @Resource
    private BusinessService businessService;
    @Resource
    private PlatformService platformService;
    private static final String CERTIFICATE_CREATE = "/v1/cloud/resourcecenter/slb/certificate/create";
    private static final String CERTIFICATE_DELATE = "/v1/cloud/resourcecenter/slb/certificate/delete";

    private static final String CERTIFICATE_INIT_STATUS = "1";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SlbCertificateDTO dto) {
        log.info("创建负载均衡监听关联证书param:{}",JSON.toJSONString(dto));
        Precondition.checkArgument(dto != null,"发送创建负载均衡监听关联证书参数不能为空");
        Long tenantId = querybillId(dto.getBusinessSystemId(),dto.getRegionCode());
        Precondition.checkArgument(tenantId != null,"发送创建负载均衡监听关联证书计费号不能为空");

        String url = resourceCenterApiUrl + CERTIFICATE_CREATE;
        HashMap<String,Object> params = new HashMap();
        params.put("regionCode",dto.getRegionCode());
        params.put("tenantId",tenantId);
        params.put("globalId",IdUtil.fastSimpleUUID());
        params.put("certName",dto.getCertificateName());
        params.put("certType",dto.getCertificateType());
        params.put("certificate",dto.getPublicKeyContent());
        params.put("privateKey",dto.getPrivateKeyContent());

        //调用平台服务创建负载均衡证书
        Mapper responseMapper = OkHttpsUtils.http()
                .sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("发送创建负载均衡监听关联证书请求结果: {}", responseMapper);
        Precondition.checkArgument(responseMapper != null,"发送创建负载均衡监听关联证书请求返回结果为空");
        String successStr = responseMapper.getString("success");
        Precondition.checkArgument("1".equals(successStr) || "true".equals(successStr),
                "创建负载均衡监听关联证书请求失败"+responseMapper.getString("message"));
        Mapper entity = responseMapper.getMapper("entity");
        String taskId = entity.getString("id");
        String status = entity.getString("status");
        String resourceId = entity.getString("resourceId");
        Precondition.checkArgument("SUCCESS".equalsIgnoreCase(status),"创建负载均衡监听关联证书失败"+responseMapper.getString("message"));
        try {
            TaskStatusExt taskStatusExt = new TaskStatusExt();
            taskStatusExt.setCreateStatus(status);
            taskStatusExt.setCreateTaskId(taskId);
            SlbCertificateDO slbCertificateDO = slbCertificateManagerConvert.dto2do(dto);
            slbCertificateDO.setTaskStatusExt(JSON.toJSONString(taskStatusExt));
            slbCertificateDO.setStatus(CERTIFICATE_INIT_STATUS);
            slbCertificateDO.setCreateTime(LocalDateTime.now());
            slbCertificateDO.setResourceId(resourceId);
            slbCertificateDO.setTenantId(tenantId);
            slbCertificateDO.setSlbListenerRel("{}");
            slbCertificateDO.setId(taskId);
            slbCertificateDAO.insert(slbCertificateDO);
        }catch (Exception e){
            Precondition.checkArgument(false,"存储证书出现异常，请联系工作人员！");
        }

    }




    /**
     * 获取计费号
     * @param regionCode
     * @return
     */
    private Long querybillId(String businessSystemId,String regionCode){
        CmpAppDTO cmpAppDTO = businessService.getById(Long.valueOf(businessSystemId));
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Long tenantId = platformService.getOrCreateTenantId(tenantDTO.getBillId(),regionCode);
        return tenantId;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SlbCertificateDTO dto) {
        SlbCertificateDO slbCertificateDO = slbCertificateManagerConvert.dto2do(dto);
        slbCertificateDAO.update(slbCertificateDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        SlbCertificateDO slbCertificateDO = slbCertificateDAO.getById(id);
        Precondition.checkArgument(slbCertificateDO != null, "负载均衡监听关联证书不存在");
        if(StringUtils.isAllBlank(slbCertificateDO.getResourceId())){
            Precondition.checkArgument(slbCertificateDO != null, "负载均衡监听关联证书任务不存在");
        }
        String url = resourceCenterApiUrl + CERTIFICATE_DELATE;
        HashMap<String,Object> params = new HashMap();
        params.put("slbCertId",slbCertificateDO.getResourceId());
        Mapper responseMapper = OkHttpsUtils.http()
                .sync(url)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(params))
                .post()
                .getBody()
                .toMapper();
        log.info("发送删除负载均衡监听关联证书请求结果: {}", responseMapper);
        //处理远程不存在证书本地存在证书情况
        Precondition.checkArgument(responseMapper !=null,"发送删除负载均衡监听关联证书请求返回结果为空");
        String successStr = responseMapper.getString("success");
        String code = responseMapper.getString("code");
        //删除成功或者远程证书不存在删除本地数据库中数据
        if(String.valueOf(BusinessExceptionEnum.SUCCESS.getCode()).equalsIgnoreCase(code)
                || responseMapper.getString("message").contains("证书不存在")){
            slbCertificateDAO.delete(id);
        }else{
            Precondition.checkArgument("1".equals(successStr) || "true".equals(successStr),
                    "删除负载均衡监听关联证书请求失败"+responseMapper.getString("message"));
        }
    }

    @Override
    public SlbCertificateDTO getById(String id) {
        SlbCertificateDO slbCertificateDO = slbCertificateDAO.getById(id);
        return slbCertificateManagerConvert.do2dto(slbCertificateDO);
    }

    @Override
    public List<SlbCertificateDTO> listByCreateTaskId(String createTaskId) {

        return StreamUtils.mapArray(slbCertificateDAO.listByCreateTaskId(createTaskId), slbCertificateManagerConvert::do2dto);

    }

    @Override
    public List<SlbCertificateDTO> listByCreateResourceId(String createResourceId) {

        return StreamUtils.mapArray(slbCertificateDAO.listByResourceId(createResourceId), slbCertificateManagerConvert::do2dto);

    }

    @Override
    public PageResult<SlbCertificateDTO> page(SlbCertificateQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<SlbCertificateDO> list = slbCertificateDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), slbCertificateManagerConvert::do2dto);
    }
}