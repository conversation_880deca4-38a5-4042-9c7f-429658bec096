package com.datatech.slgzt.impl.cmdb;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.datatech.slgzt.enums.CmdbModelTypeEnum;
import com.datatech.slgzt.enums.HAndoverStatusEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.cmdb.CmdbCommonRep;
import com.datatech.slgzt.model.cmdb.QueryCmdbRep;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.service.CmdbOpenApi;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CmdbHandoverService {

    @Resource
    private CmdbOpenApi cmdbOpenApi;


    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Scheduled(cron = "0 0 1 * * ?")
    public void execute() {
        log.info("入网交维状态 定时查询 start");
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setTypeList(Lists.newArrayList(ProductTypeEnum.ECS.getCode(),
                ProductTypeEnum.MYSQL.getCode(),
                ProductTypeEnum.REDIS.getCode()));
        query.setHandoverStatus(HAndoverStatusEnum.NO.getType());
        List<ResourceDetailDTO> productVoList = resourceDetailManager.list(query);
        productVoList = productVoList.stream().filter(productVo ->
                StringUtils.isNotBlank(productVo.getConfigId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productVoList)) {
            return;
        }
        for (ResourceDetailDTO resourceDetailDTO : productVoList) {
            queryVM(resourceDetailDTO.getConfigId());
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void queryVM(String instanceId) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        List<Object> list = new ArrayList<>();
        map.put("instanceId", instanceId);
        list.add(map);
        map1.put("$and", list);
        Map<String, Object> map2 = new HashMap<>();
        map2.put("query", map1);
        map2.put("page", 1);
        String queryParam = JSONObject.toJSONString(map2, SerializerFeature.WriteMapNullValue);
        log.info("queryVM 云主机查询参数：{}", queryParam);
        CmdbCommonRep<QueryCmdbRep<JSONObject>> total = cmdbOpenApi.queryCmdbData(CmdbModelTypeEnum.VM.getCode(), queryParam);
        log.info("queryVM 云主机查询返回 : {}", JSONObject.toJSONString(total));
        if (!"0".equals(total.getCode())) {
            log.error("失败：queryVM ecs err : {}", JSONObject.toJSONString(total));
            return;
        }
        List<JSONObject> vms = total.getData().getList();
        if (CollectionUtils.isEmpty(vms)){
            return;
        }
        JSONObject jsonObject = vms.get(0);
        String handover = jsonObject.getString("MAINTENANCE_HANDOVER");
        if ("是(完全移交)".equals(handover)) {
            resourceDetailManager.updateHandoverStatusByConfigId(instanceId, HAndoverStatusEnum.YES.getType());
        }
    }

}
