package com.datatech.slgzt.enums;

import lombok.Getter;

/**
 * 工单类型的枚举
 */
@Getter
public enum OrderTypeEnum {

    /**
     * 订单类型枚举
     */
    SUBSCRIBE("subscribe","BZ", "资源开通"),
    NON_STANDARD("nonStandardSubscribe", "FB","资源开通非标"),
    CHANGE("change", "BG","资源变更"),
    RECOVERY("recovery", "HS","资源回收"),
    EXTERNAL("external", "EX","外部资源"),
    DAG("dag", "DAG","DAG图"),
    MODIFY("modify", "BG","资源变更"),
    //对公
    CORPORATE("corporate", "DG","对公资源开通"),
    /**
     * 资源退订订单
     */
    UNSUBSCRIBE("unsubscribe", "DEF","退订"),
    DELAY("delay", "DEF" ,"资源延期"),
    RECOVERY_DELAY("recovery_delay", "DEF", "资源回收/延期"),
    UNKNOWN("unknown", "DEF", "-"),
    ;

    private final String code;

    //前缀
    private final String prefix;

    private final String message;

    OrderTypeEnum(String code, String prefix, String message) {
        this.code = code;
        this.prefix = prefix;
        this.message = message;
    }

    public static OrderTypeEnum getByCode(String code) {
        for (OrderTypeEnum value : OrderTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return OrderTypeEnum.UNKNOWN;
    }

    /**
     * 分割订单号前缀
     * 获取对应的订单类型
     */
    public static OrderTypeEnum getOrderType(String orderNo) {
        for(OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if (orderNo.startsWith(orderTypeEnum.getPrefix())) {
                return orderTypeEnum;
            }
        }
        return OrderTypeEnum.UNKNOWN;
    }
}
