package com.datatech.slgzt.controller.security;

import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.WocSecurityGroupWebConvert;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.manager.VpcOrderManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.SecurityGroupOperateDTO;
import com.datatech.slgzt.model.dto.WocSecurityGroupDTO;
import com.datatech.slgzt.model.query.SecurityGroupQuery;
import com.datatech.slgzt.model.req.security.*;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.security.WocSecurityGroupVO;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.service.security.WocSecurityGroupService;
import com.datatech.slgzt.utils.*;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-04-29 10:25
 **/
@RestController
@RequestMapping("/securityGroup")
public class WocSecurityGroupController {

    @Resource
    private WocSecurityGroupService wocSecurityGroupService;

    @Resource
    private WocSecurityGroupWebConvert convert;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private TenantManager tenantManager;


    @PostMapping("/create")
    @OperationLog(description = "创建安全组",operationType = "CREATE")
    public CommonResult<String> createSecurityGroup(@RequestBody WocSecurityGroupCreateReq req) {
        Precondition.checkArgument(req.getName(), "名称不能为空");
        Precondition.checkArgument(req.getCatalogueDomainCode(), "云类型不能为空");
        Precondition.checkArgument(req.getRegionCode(), "regionCode不能为空");
        Precondition.checkArgument(req.getVpcId(), "vpc不能为空");
        List<VpcOrderResult> vpcOrderResults = vpcOrderManager.listByIdList(Arrays.asList(req.getVpcId()));
        Precondition.checkArgument(vpcOrderResults, "找不到对应的vpc");
        req.getRuleList().forEach(rule -> {
            Precondition.checkArgument(IPInSubnetChecker.validatePortRange(rule.getPortRange()), "端口格式不合规");
            Precondition.checkArgument(IPInSubnetChecker.validateIpList(rule.getAccreditIp()), "IP格式不合规");
        });
        WocSecurityGroupDTO wocSecurityGroupDTO = convert.convert(req);
        wocSecurityGroupDTO.setTenantId(vpcOrderResults.get(0).getTenantId());
        return CommonResult.success(wocSecurityGroupService.createWocSecurityGroup(wocSecurityGroupDTO) ? "安全组创建成功" : "安全组创建中");
    }



    @PostMapping("/page")
    public CommonResult<PageResult<WocSecurityGroupVO>> querySecurityGroupListPage(@RequestBody WocSecurityGroupPageReq req) {
        SecurityGroupQuery securityGroupQuery = convert.convert(req);
        Long currentUserId = UserHelper.INSTANCE.getCurrentUserId();
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        List<Long> tenantIds = tenantManager.listTenIdByOwnerId(currentUserId);
        if (ObjNullUtils.isNull(tenantIds)) {
            tenantIds= Lists.newArrayList(-1L);
        }
        List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
        List<String> roles = StreamUtils.mapArray(oacRoles, UserCenterRoleDTO::getCode);
        if (!roles.contains("super_admin") && !roles.contains("operation_group")) {
            securityGroupQuery.setTenantIdList(tenantIds);
        }
        PageResult<WocSecurityGroupDTO> page = wocSecurityGroupService.page(securityGroupQuery);
        return CommonResult.success(PageWarppers.box(page, convert::convert));
    }

    @PostMapping("/operate")
    @OperationLog(description = "安全组操作",operationType = "UPDATE")
    public CommonResult<String> operateSecurityGroup(@RequestBody WocSecurityGroupOperateReq req) {
        Precondition.checkArgument(req.getOperateType(), "操作类型不能为空");
        Precondition.checkArgument(req.getRegionCode(), "云区域编码不能为空");
        Precondition.checkArgument(req.getBusinessSystemId(), "业务系统id不能为空");
        Precondition.checkArgument(req.getVmId(), "云主机id不能为空");
        Precondition.checkArgument(req.getSecurityGroupIds(), "安全组不能为空");
        SecurityGroupOperateDTO securityGroupOperateDTO = convert.convert(req);
        wocSecurityGroupService.operateSecurityGroup(securityGroupOperateDTO);
        return CommonResult.success("操作成功");
    }

    @PostMapping("/delete")
    @OperationLog(description = "安全组删除",operationType = "DELETE")
    public CommonResult<String> deleteSecurityGroup(@RequestBody WocSecurityGroupDeleteReq req) {
        Precondition.checkArgument(req.getIds(), "安全组id不能为空");
        wocSecurityGroupService.deleteSecurityGroup(req.getIds());
        return CommonResult.success("删除成功");
    }

    @GetMapping("/detail")
    public CommonResult<WocSecurityGroupVO> detail(Long id) {
        return CommonResult.success(convert.convert(wocSecurityGroupService.detail(id)));
    }

    @PostMapping("/createRule")
    public CommonResult<String> createRule(@RequestBody WocSecurityGroupRuleReq req){
        Precondition.checkArgument(req.getId(), "安全组id不能为空");
        return CommonResult.success(wocSecurityGroupService.createRule(convert.convert(req)) ? "安全组规则创建成功" : "安全组规则创建中");
    }

    @PostMapping("/updateRule")
    public CommonResult<String> updateRule(@RequestBody WocSecurityGroupRuleReq req){
        Precondition.checkArgument(req.getId(), "安全组id不能为空");
        return CommonResult.success(wocSecurityGroupService.updateRule(convert.convert(req)) ? "安全组规则修改成功" : "安全组规则修改中");
    }

    @PostMapping("/deleteRule")
    public CommonResult<String> deleteRule(@RequestBody WocSecurityGroupRuleReq req){
        Precondition.checkArgument(req.getId(), "安全组id不能为空");
        wocSecurityGroupService.deleteRule(convert.convert(req));
        return CommonResult.success("安全组规则删除成功");
    }

}
